/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "images_with_text_scrolling_yFGt6Y": {
      "type": "images-with-text-scrolling-hero",
      "blocks": {
        "item_ipJMyQ": {
          "type": "item",
          "settings": {
            "image": "shopify://shop_images/opensource-clarity-hydration-electrolyte-pods-three-flavors-300mg-magnesium.webp",
            "mobile_image": "shopify://shop_images/opensource-clarity-hydration-electrolyte-pods-three-flavors-300mg-magnesium-mobile.webp",
            "star_count": 0,
            "title": "FEEL IT",
            "subheading": "TRIPLE MAGNESIUM ELECTROLYTES FOR CALM ENERGY AND CLARITY",
            "button_text": "[SHOP ELECTROLYTES]",
            "button_url": "shopify://products/triple-magnesium-electrolyte-pods-300mg",
            "button_style": "primary",
            "button_color": "#47de47",
            "button_text_color": "#2a2a2a",
            "content": ""
          }
        }
      },
      "block_order": [
        "item_ipJMyQ"
      ],
      "custom_css": [
        ".images-scrolling-desktop__media-wrapper {z-index: 4;}"
      ],
      "settings": {
        "full_width": true,
        "stack_on_mobile": true,
        "show_counter": false,
        "desktop_image_effect": "reveal",
        "image_position": "end",
        "show_floating_star": false,
        "star_size": 40,
        "star_mobile_top_position": -60,
        "star_mobile_right_position": 60,
        "star_desktop_top_position": 300,
        "star_desktop_right_position": -90,
        "star_opacity": 1,
        "subheading_font_family": "body",
        "subheading_font_size_mobile": 20,
        "subheading_font_size_desktop": 20,
        "subheading_font_weight": "normal",
        "subheading_mobile_max_width": 75,
        "rating_text": "1000+ DIALED IN",
        "background": "",
        "background_gradient": "",
        "text_color": "rgba(0,0,0,0)",
        "heading_color": "",
        "heading_gradient": ""
      }
    },
    "scroll_banner_xxdKei": {
      "type": "scroll-banner",
      "blocks": {
        "shape_9EnipM": {
          "type": "shape",
          "settings": {
            "shape": "star",
            "text": "0",
            "link_url": "",
            "rotate_on_scroll": true,
            "rotate_text_on_scroll": false,
            "shape_color": "#2a2a2a",
            "color": "#e7e8e5",
            "shape_width": 100,
            "shape_width_mobile": 60,
            "text_size": 100,
            "text_size_mobile": 50
          }
        },
        "text_eyHqGL": {
          "type": "text",
          "settings": {
            "text": "<p>SECRETS</p>",
            "link_url": "",
            "external_link": false,
            "text_size": 40,
            "text_size_mobile": 20,
            "color": "#e7e8e5"
          }
        },
        "shape_bVddyC": {
          "type": "shape",
          "settings": {
            "shape": "star",
            "text": "0",
            "link_url": "",
            "rotate_on_scroll": true,
            "rotate_text_on_scroll": false,
            "shape_color": "#2a2a2a",
            "color": "#e7e8e5",
            "shape_width": 100,
            "shape_width_mobile": 60,
            "text_size": 100,
            "text_size_mobile": 50
          }
        },
        "text_eGmwq6": {
          "type": "text",
          "settings": {
            "text": "<p>SUGARS</p>",
            "link_url": "",
            "external_link": false,
            "text_size": 40,
            "text_size_mobile": 20,
            "color": "#e7e8e5"
          }
        },
        "shape_kyYdPN": {
          "type": "shape",
          "settings": {
            "shape": "star",
            "text": "0",
            "link_url": "",
            "rotate_on_scroll": true,
            "rotate_text_on_scroll": false,
            "shape_color": "#2a2a2a",
            "color": "#e7e8e5",
            "shape_width": 100,
            "shape_width_mobile": 60,
            "text_size": 100,
            "text_size_mobile": 50
          }
        },
        "text_mNeJg9": {
          "type": "text",
          "settings": {
            "text": "<p>FILLERS</p>",
            "link_url": "",
            "external_link": false,
            "text_size": 40,
            "text_size_mobile": 20,
            "color": "#e7e8e5"
          }
        }
      },
      "block_order": [
        "shape_9EnipM",
        "text_eyHqGL",
        "shape_bVddyC",
        "text_eGmwq6",
        "shape_kyYdPN",
        "text_mNeJg9"
      ],
      "custom_css": [
        ".path-container {transform: translateX(-700px);}"
      ],
      "settings": {
        "color_bg": "#2a2a2a",
        "color_border": "#2a2a2a",
        "border_width": 0,
        "reverse_direction": true,
        "scroll_speed_desktop": 40,
        "scroll_speed_mobile": 20,
        "padding_top": 30,
        "padding_bottom": 30,
        "margin_top": 20,
        "margin_bottom": 20,
        "css_class": "",
        "custom_css": ""
      }
    },
    "scores_progress_E7kGJ7": {
      "type": "scores-progress",
      "blocks": {
        "progressbar_3rgnCm": {
          "type": "progressbar",
          "settings": {
            "progress_value": "75",
            "progress_bottom_title": "Magnesium Deficiency",
            "progress_abs_image": "shopify://shop_images/Rectangle.png",
            "progress_abs_description": "Up to 75% of Americans are deficient in magnesium."
          }
        },
        "progressbar_rL9Ct3": {
          "type": "progressbar",
          "settings": {
            "progress_value": "95",
            "progress_bottom_title": "Enzymes Need Magnesium",
            "progress_abs_image": "shopify://shop_images/Rectangle.png",
            "progress_abs_description": "Over 95% of your body's enzymes require magnesium as a cofactor."
          }
        },
        "progressbar_zMLmrU": {
          "type": "progressbar",
          "settings": {
            "progress_value": "71",
            "progress_bottom_title": "OPENSOURCE PROVIDES",
            "progress_abs_image": "shopify://shop_images/Rectangle.png",
            "progress_abs_description": "OS Electrolyte Pods have 300mg of magnesium, which is 71% of the RDI."
          }
        },
        "progressbar_Vj7PpB": {
          "type": "progressbar",
          "settings": {
            "progress_value": "80",
            "progress_bottom_title": "Magnesium Absorption",
            "progress_abs_image": "shopify://shop_images/Rectangle.png",
            "progress_abs_description": "We use the most bioavailable forms of magnesium with up to 80% absorption."
          }
        }
      },
      "block_order": [
        "progressbar_3rgnCm",
        "progressbar_rL9Ct3",
        "progressbar_zMLmrU",
        "progressbar_Vj7PpB"
      ],
      "name": "Scores Progress",
      "settings": {
        "scores_progres_title": "benefits in every sip",
        "scores_progres_sub_title": "",
        "padding_top": 0,
        "padding_bottom": 0
      }
    },
    "ss_comparison_table_12_NhfG6r": {
      "type": "ss-comparison-table-12",
      "blocks": {
        "table_row_w4hQdx": {
          "type": "table_row",
          "settings": {
            "row_heading": "300mg of Magnesium",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_JGPD7n": {
          "type": "table_row",
          "settings": {
            "row_heading": "Reduce Brain Fog",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_pd4bwh": {
          "type": "table_row",
          "settings": {
            "row_heading": "Minimize Bloat",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_PY3VVn": {
          "type": "table_row",
          "settings": {
            "row_heading": "Lower Blood Pressure",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_3ndXYW": {
          "type": "table_row",
          "settings": {
            "row_heading": "Trace Minerals",
            "first_column": "",
            "first_column_icon": "check",
            "second_column": "",
            "second_column_icon": "cross",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_zq9fUB": {
          "type": "table_row",
          "settings": {
            "row_heading": "Fillers",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "none",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_KzM7n7": {
          "type": "table_row",
          "settings": {
            "row_heading": "Sugar",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        },
        "table_row_TrGQNE": {
          "type": "table_row",
          "settings": {
            "row_heading": "Preservatives",
            "first_column": "",
            "first_column_icon": "cross",
            "second_column": "",
            "second_column_icon": "check",
            "third_column": "",
            "third_column_icon": "check",
            "four_column": "",
            "four_column_icon": "check",
            "fives_column": "",
            "fives_column_icon": "cross",
            "sixth_column": "",
            "sixth_column_icon": "cross"
          }
        }
      },
      "block_order": [
        "table_row_w4hQdx",
        "table_row_JGPD7n",
        "table_row_pd4bwh",
        "table_row_PY3VVn",
        "table_row_3ndXYW",
        "table_row_zq9fUB",
        "table_row_KzM7n7",
        "table_row_TrGQNE"
      ],
      "settings": {
        "comparison_table_image": "shopify://shop_images/Mask_Group_3.png",
        "content_align": "left",
        "content_align_mobile": "center",
        "heading": "<p>It's Not Even Close</p>",
        "heading_custom": false,
        "heading_font": "josefin_sans_n4",
        "heading_size": 62,
        "heading_size_mobile": 28,
        "heading_height": 130,
        "text": "",
        "text_custom": false,
        "text_font": "josefin_sans_n4",
        "text_size": 18,
        "text_size_mobile": 14,
        "text_height": 150,
        "text_mt": 16,
        "text_mt_mobile": 16,
        "table_columns": 2,
        "table_radius": 20,
        "table_mt": 24,
        "table_mt_mobile": 12,
        "column_width": 250,
        "column_width_mobile": 120,
        "row_heading_width": 260,
        "row_heading_width_mobile": 150,
        "first_heading": "OS",
        "first_heading_image": "shopify://shop_images/icon-logo-star.svg",
        "first_heading_url": "",
        "second_heading": "OTHERS",
        "second_heading_image": "shopify://shop_images/sadface.svg",
        "second_heading_url": "",
        "third_heading": "",
        "third_heading_url": "",
        "four_heading": "",
        "four_heading_url": "",
        "fives_heading": "",
        "fives_heading_url": "",
        "sixth_heading": "",
        "sixth_heading_url": "",
        "heading_image_size": 70,
        "heading_image_size_mobile": 80,
        "heading_image_radius": 0,
        "heading_image_border_thickness": 0,
        "column_heading_custom": false,
        "column_heading_font": "assistant_n4",
        "column_heading_size": 24,
        "column_heading_size_mobile": 14,
        "column_heading_height": 130,
        "column_heading_align": "center",
        "column_heading_align_mobile": "center",
        "column_heading_padding_horizontal": 16,
        "column_heading_padding_horizontal_mobile": 4,
        "column_heading_padding_vertical": 24,
        "column_heading_padding_vertical_mobile": 20,
        "row_padding_horizontal": 16,
        "row_padding_horizontal_mobile": 4,
        "row_padding_vertical": 24,
        "row_padding_vertical_mobile": 20,
        "row_border_thickness": 1,
        "row_heading_custom": false,
        "row_heading_font": "assistant_n4",
        "row_heading_size": 22,
        "row_heading_size_mobile": 18,
        "row_heading_height": 130,
        "value_custom": false,
        "value_font": "assistant_n4",
        "value_size": 24,
        "value_size_mobile": 14,
        "value_height": 130,
        "value_icon_size": 28,
        "value_icon_size_mobile": 24,
        "first_column_icon_size": 28,
        "first_column_icon_size_mobile": 24,
        "second_column_icon_size": 22,
        "second_column_icon_size_mobile": 20,
        "third_column_icon_size": 28,
        "third_column_icon_size_mobile": 24,
        "versus_icon_size": 60,
        "versus_icon_size_mobile": 30,
        "bottom_text": "",
        "bottom_text_custom": false,
        "bottom_text_font": "josefin_sans_n4",
        "bottom_text_size": 12,
        "bottom_text_size_mobile": 12,
        "bottom_text_height": 150,
        "bottom_text_mt": 24,
        "bottom_text_mt_mobile": 20,
        "bottom_text_align": "left",
        "bottom_text_align_mobile": "left",
        "heading_color": "#2a2a2a",
        "text_color": "#2a2a2a",
        "bottom_text_color": "#afabab",
        "column_heading_color": "#2a2a2a",
        "column_heading_active_color": "#2a2a2a",
        "value_color": "#2a2a2a",
        "value_active_color": "#2a2a2a",
        "table_column_bg_color": "#e7e8e5",
        "table_column_active_bg_color": "#47de47",
        "heading_image_border_color": "#2a2a2a",
        "row_heading_color": "#2a2a2a",
        "row_border_color": "rgba(0,0,0,0)",
        "value_icon_color": "#2a2a2a",
        "value_icon_active_color": "#2a2a2a",
        "versus_value_icon_color": "#ffffff",
        "versus_icon_bg_color": "#1a1a1a",
        "versus_vs_bg_color": "#2a2a2a",
        "background_color": "#e7e8e5",
        "background_gradient": "",
        "border_color": "#2a2a2a",
        "margin_top": 0,
        "margin_bottom": 0,
        "margin_horizontal": 0,
        "margin_horizontal_mobile": 0,
        "padding_top": 0,
        "padding_bottom": 0,
        "padding_horizontal": 0,
        "padding_horizontal_mobile": 0,
        "full_width": true,
        "content_width": 400,
        "border_thickness": 0,
        "section_radius": 0,
        "lazy": true
      }
    },
    "image_card_slider_69RmEV": {
      "type": "image-card-slider",
      "blocks": {
        "card_block_pXDWUE": {
          "type": "card-block",
          "settings": {
            "card_image": "shopify://shop_images/Image.png",
            "card_title": "Magnesium malate",
            "card_description": "<p>ENERGIZING</p>",
            "section_card_description": "<p>Magnesium combined with malic acid, a natural compound found in fruits like apples. This unique pairing creates one of the most bioavialable forms of magnesium, with studies showing up to 40% better absorption than magnesium oxide. This form excels at supporting energy production and muscle recovery.</p>"
          }
        },
        "card_block_8bXL4J": {
          "type": "card-block",
          "settings": {
            "card_image": "shopify://shop_images/Rectangle_c068abca-f52c-4799-87c9-4bbed0f07971.png",
            "card_title": "Magnesium glycinate",
            "card_description": "<p>calming</p>",
            "section_card_description": "<p>Magnesium combined with glycine, an essential amino acid that acts as anatural calmingneurotransmitter. This form has up to 80% absorption and genteleness on the digestive system. The glycine helps transport magnesium directly to cells while providing additional benefits forsleep qualityand nervous system functioning.</p>"
          }
        },
        "card_block_9qrYUF": {
          "type": "card-block",
          "settings": {
            "card_image": "shopify://shop_images/Rectangle_1.png",
            "card_title": "Magnesium taurate",
            "card_description": "<p>focused</p>",
            "section_card_description": "<p>Magnesium combined with taurine, an amino acid that plays a crucial role in neurotransmitter function and brain health. Magnesium taurate crosses the blood-brain barrier, supporting mental clarity and cognitive function. Studies indicate high bioavailabiility, with absorption rates around 65-70%.</p>"
          }
        },
        "card_block_HLQhmn": {
          "type": "card-block",
          "settings": {
            "card_image": "shopify://shop_images/himalayas.jpg",
            "card_title": "himalayan salt",
            "card_description": "<p>balanced</p>",
            "section_card_description": "<p>Sources from ancient sea beds in the Himalayan mountains, crystallized over 250 million years ago and protected from modern environmental contaminants. This contains a natural spectrum of over 84 trace minerals including Boron, Selenium, Manganese, Zinc, and Phosphorus.</p>"
          }
        },
        "card_block_jhJQei": {
          "type": "card-block",
          "settings": {
            "card_image": "shopify://shop_images/sweat.jpg",
            "card_title": "potassium citrate",
            "card_description": "<p>HYDRATING</p>",
            "section_card_description": "<p>Potassium combined with citric acid, creating a highly bioavailable form that excels at supporting pH balance and hydration at the cellular level. Studies indicate absorption rates of up to 90%, making it one of the most efficient forms for maintaining optimal potassium levels.</p>"
          }
        },
        "card_block_VayA9z": {
          "type": "card-block",
          "settings": {
            "card_image": "shopify://shop_images/plant.jpg",
            "card_title": "stevia extract",
            "card_description": "<p>NATURAL</p>",
            "section_card_description": "<p>Unlike artificial sweeteners, stevia is extracted from natural plant compounds called steviol glycosides, which are up to 300 times sweeter than sugar. This zero-calorie natural sweetener has a glycemic index of zero, meaning it won't spike blood sugar levels. Research indicates stevia may actually support healthy glucose metabolism, setting it apart from artificial alternatives.</p>"
          }
        },
        "card_block_xQBHEN": {
          "type": "card-block",
          "settings": {
            "card_image": "shopify://shop_images/raspberry4.jpg",
            "card_title": "natural flavors",
            "card_description": "<p>MOUTHWATERING</p>",
            "section_card_description": "<p>Derived entirely from plant sources like fruit and botanicals. Unlike artificial flavors created in labs from synthetic chemicals, natural flavors are extracted from real food sources. Each flavor component must meet our strict criteria:no artificial preservatives, no synthetic carriers, and no artificial solvents.We specifically select flavors that use only natural extraction methods and organic compounds to create their taste profiles.</p>"
          }
        }
      },
      "block_order": [
        "card_block_pXDWUE",
        "card_block_8bXL4J",
        "card_block_9qrYUF",
        "card_block_HLQhmn",
        "card_block_jhJQei",
        "card_block_VayA9z",
        "card_block_xQBHEN"
      ],
      "name": "Image Card Slider",
      "settings": {
        "section_title": "What's Inside?",
        "image_ratio": "portrait",
        "padding_top": 0,
        "padding_bottom": 0
      }
    },
    "new_card_slider_g8HTtW": {
      "type": "new-card-slider",
      "blocks": {
        "card_block_wW6PPq": {
          "type": "card-block",
          "settings": {
            "card_title": "GLYPHOSATE",
            "card_subtitle": "(PMG)",
            "card_description": "<p>Pesticide linked to cancer risk and gut dysbiosis.</p>",
            "card_purity": "<p>NOT DETECTED</p>"
          }
        },
        "card_block_MtAgh4": {
          "type": "card-block",
          "settings": {
            "card_title": "Arsenic",
            "card_subtitle": "(As)",
            "card_description": "<p>Carcinogenic metalloid causing skin damage and organ failure.</p>",
            "card_purity": "<p>&lt;0.060ppm</p>"
          }
        },
        "card_block_cKGnM6": {
          "type": "card-block",
          "settings": {
            "card_title": "Cadmium",
            "card_subtitle": "(Cd)",
            "card_description": "<p>Carcinogenic metal causing kidney damage and bone demineralization</p>",
            "card_purity": "<p>&lt;0.001ppm</p>"
          }
        },
        "card_block_HJx7Eq": {
          "type": "card-block",
          "settings": {
            "card_title": "Lead",
            "card_subtitle": "(Pb)",
            "card_description": "<p>Neurotoxic heavy metal impairing cognition and causing anemia</p>",
            "card_purity": "<p>&lt;0.014ppm</p>"
          }
        },
        "card_block_tq3hXH": {
          "type": "card-block",
          "settings": {
            "card_title": "Mercury",
            "card_subtitle": "(Hg)",
            "card_description": "<p>Neurotoxin causing neurological damage and developmental defects</p>",
            "card_purity": "<p>&lt;0.005ppm</p>"
          }
        }
      },
      "block_order": [
        "card_block_wW6PPq",
        "card_block_MtAgh4",
        "card_block_cKGnM6",
        "card_block_HJx7Eq",
        "card_block_tq3hXH"
      ],
      "name": "New Card Slider",
      "settings": {
        "section_title": "verified clean",
        "section_description": "<p>Our products undergo rigorous third-party testing to ensure non-contamination and the cleanest ingredients. </p>",
        "padding_top": 0,
        "padding_bottom": 0
      }
    },
    "new_slider_M3benk": {
      "type": "new-slider",
      "blocks": {
        "magnesium_Bpjd8d": {
          "type": "magnesium",
          "settings": {
            "title": "Magnesium Type",
            "subtitle": "Effect"
          }
        },
        "magnesium_BnmEjC": {
          "type": "magnesium",
          "settings": {
            "title": "Magnesium Type",
            "subtitle": "Effect"
          }
        },
        "magnesium_w8qnYn": {
          "type": "magnesium",
          "settings": {
            "title": "Magnesium Type",
            "subtitle": "Effect"
          }
        },
        "magnesium_YRbq8m": {
          "type": "magnesium",
          "settings": {
            "title": "Magnesium Type",
            "subtitle": "Effect"
          }
        },
        "magnesium_xfndjj": {
          "type": "magnesium",
          "settings": {
            "title": "Magnesium Type",
            "subtitle": "Effect"
          }
        }
      },
      "block_order": [
        "magnesium_Bpjd8d",
        "magnesium_BnmEjC",
        "magnesium_w8qnYn",
        "magnesium_YRbq8m",
        "magnesium_xfndjj"
      ],
      "disabled": true,
      "name": "New Slider",
      "settings": {
        "title": "What's Inside?",
        "description": "Each form of magnesium supports your body differently. Discover which is right for your needs."
      }
    },
    "instagram_section_MHCLtJ": {
      "type": "instagram-section",
      "blocks": {
        "image_4qcg8F": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/1c436b6e85a109976de79f989ac257c7a3fca83d.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_VpDXGK": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Image_18.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_wWBhwq": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Image_19.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_kxgCim": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Image_20.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_nBbBpi": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Image_22.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_mHMn8L": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Image_21.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        }
      },
      "block_order": [
        "image_4qcg8F",
        "image_VpDXGK",
        "image_wWBhwq",
        "image_kxgCim",
        "image_nBbBpi",
        "image_mHMn8L"
      ],
      "name": "Instagram",
      "settings": {
        "hedding": "",
        "animation_direction": "normal",
        "animation_speed": 60,
        "section_padding_top": 0,
        "section_padding_bottom": 0,
        "space_between_blocks_desktop": 35,
        "space_between_blocks_mobile": 20,
        "rotate": "0"
      }
    },
    "instagram_section_fp3HN4": {
      "type": "instagram-section",
      "blocks": {
        "image_fDbLK8": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/40681a96b04bc7c13d77812eeff812732614c131.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_b47Bgw": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/72321c7e5f7b8365f7866c2b3fd45688c4f1dbd0.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_HrTfEW": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/20bafd3bfb761f87accc206d2df4a541f6592377.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_jR8tze": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/8e8675fa4a83bc13045b0f36214db4f88e48e123.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_aJiqbQ": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/Image_23.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        },
        "image_iBQcFH": {
          "type": "image",
          "settings": {
            "image": "shopify://shop_images/5ad3fbbdc51d152bf3daafb9a68463ecc2c8880a.png",
            "height_desktop": 260,
            "height_mobile": 156,
            "link": "https://www.instagram.com/os.lytes"
          }
        }
      },
      "block_order": [
        "image_fDbLK8",
        "image_b47Bgw",
        "image_HrTfEW",
        "image_jR8tze",
        "image_aJiqbQ",
        "image_iBQcFH"
      ],
      "name": "Instagram",
      "settings": {
        "hedding": "SEE YOU AT THE CLUB @os.lytes",
        "animation_direction": "reverse",
        "animation_speed": 60,
        "section_padding_top": 0,
        "section_padding_bottom": 0,
        "space_between_blocks_desktop": 35,
        "space_between_blocks_mobile": 20,
        "rotate": "0"
      }
    },
    "faq_9CFhia": {
      "type": "faq",
      "blocks": {
        "item_a9GK4W": {
          "type": "item",
          "settings": {
            "title": "WHY THREE FORMS OF MAGNESIUM?",
            "content": "<p>Each has distinct biochemical properties: </p><p><strong>Magnesium Taurate: </strong>for <em>focus</em> and neurological function</p><p><strong>Magnesium Glycinate</strong>: for sleep quality and <em>calming</em> effects on the nervous system</p><p><strong>Magnesium Malate: </strong>for ATP production and <em>energy</em> metabolism</p><p></p>"
          }
        },
        "item_93EVNt": {
          "type": "item",
          "settings": {
            "title": "HOW DO I VERIFY YOUR TEST RESULTS?",
            "content": "<p>By clicking <em>here.</em> </p>"
          }
        },
        "item_MNXpd4": {
          "type": "item",
          "settings": {
            "title": "WHAT MAKES YOUR ELECTROLYTES DIFFERENT?",
            "content": "<ol><li>We deliver a substantial <em>300mg of magnesium</em> per serving using three highly bioavailable forms. While others may sneak in low doses of magnesium, ours has <em>enough for you to feel.</em>  </li><li>We publish <em>third-party test results</em> for every batch, <em>verifying active ingredients</em> and <em>absense of heavy metals</em>.</li><li> We've <em>eliminated unnecessary additives</em> common in other products - 0 sugar, 0 artificial sweeteners, 0 fillers, 0 preservatives, 0 artificial colors.</li><li>Our electrolyte profile reflects <em>optimal mineral ratios</em> for cellular function rather than simply following industry norms.  </li><li>Our flavors are <em>downright addicting</em>. </li></ol>"
          }
        },
        "item_JQWQ3K": {
          "type": "item",
          "settings": {
            "title": "WHY IS THERE 10 CALORIES IF THERE IS NO SUGAR?",
            "content": "<p>Magnesium taurate contains taurate (an amino acid), and magnesium glycinate contains glycine (another amino acid). Both provide approximately 4 calories per gram, just like protein. </p><p>Additionally, citric acid is used as a flavoring agent. It contains 3 calories per gram and has a low glycemic index.<br/><br/>Together, these will have a negligible impact on blood sugar making OS Electrolyte Pods ketogenic, low-carb, and diabetic friendly. </p>"
          }
        },
        "item_GeeC4b": {
          "type": "item",
          "settings": {
            "title": "HOW MANY CAN I HAVE PER DAY?",
            "content": "<p>Most users function optimally with 1-2 sticks daily. Each dosage loads you up with 300mg of magnesium, so we recommend staying under 3 sticks per day. </p>"
          }
        },
        "item_6GfVhV": {
          "type": "item",
          "settings": {
            "title": "HOW LONG WILL IT TAKE TO GET MY ORDER?",
            "content": "<p>It depends on where you are. Orders processed here will take 2-3 business days to arrive in lower 48 states. Overseas deliveries can take anywhere from 7-10 days. Delivery details will be provided in your confirmation email.</p>"
          }
        }
      },
      "block_order": [
        "item_a9GK4W",
        "item_93EVNt",
        "item_MNXpd4",
        "item_JQWQ3K",
        "item_GeeC4b",
        "item_6GfVhV"
      ],
      "custom_css": [
        "/* fkDevS */.accordion-box {background: transparent;}",
        ".accordion {border-bottom: 2px solid; padding-top: 10px; padding-bottom: 10px;}",
        ".accordion__toggle:first-child {font-size: 1.5rem;}",
        "@media (max-width: 500px) {.accordion__toggle:first-child {font-size: 1.2rem; }}",
        " /* fkDevE */"
      ],
      "settings": {
        "full_width": true,
        "subheading": "",
        "title": "FAQS",
        "content": "",
        "team_avatar_width": 160,
        "support_hours": "",
        "answer_time": "",
        "button_text": "",
        "button_url": "",
        "text_position": "center",
        "background": "",
        "background_gradient": "",
        "text_color": "#2a2a2a",
        "heading_color": "#2a2a2a",
        "heading_gradient": "",
        "button_background": "",
        "button_text_color": "",
        "accordion_background": "",
        "accordion_text_color": ""
      }
    }
  },
  "order": [
    "images_with_text_scrolling_yFGt6Y",
    "scroll_banner_xxdKei",
    "scores_progress_E7kGJ7",
    "ss_comparison_table_12_NhfG6r",
    "image_card_slider_69RmEV",
    "new_card_slider_g8HTtW",
    "new_slider_M3benk",
    "instagram_section_MHCLtJ",
    "instagram_section_fp3HN4",
    "faq_9CFhia"
  ]
}
