/* Contact Page Styling - Clean Design */

/* Main contact section */
.shopify-section--contact {
  background: #E7E8E5 !important;
  padding: 3rem 0 !important;
}

/* Section container */
.shopify-section--contact .section-stack {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 2rem !important;
}

/* Contact form container */
.contact-form {
  background: #E7E8E5 !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 12px !important;
  padding: 2.5rem !important;
  box-shadow: none !important;
}

/* Subheading styling */
.shopify-section--contact .subheading {
  font-family: var(--heading-font-family) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  color: #2A2A2A !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  margin-bottom: 0.5rem !important;
}

/* Main heading styling */
.shopify-section--contact .h2,
.shopify-section--contact h2 {
  font-family: var(--heading-font-family) !important;
  font-weight: 700 !important;
  font-size: 3rem !important;
  color: #2A2A2A !important;
  margin-bottom: 1rem !important;
  line-height: 1.1 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Content text styling */
.shopify-section--contact .prose p {
  font-family: var(--text-font-family) !important;
  font-size: 1rem !important;
  color: #2A2A2A !important;
  line-height: 1.6 !important;
  margin-bottom: 2rem !important;
}

/* Form fieldset */
.contact-form .fieldset {
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Input row styling */
.contact-form .input-row {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

/* Input field styling */
.contact-form .input,
.contact-form .select {
  margin-bottom: 1.5rem !important;
}

.contact-form .input__field,
.contact-form .select__field,
.contact-form textarea {
  width: 100% !important;
  padding: 0.875rem 1rem !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  background: #FFFFFF !important;
  color: #2A2A2A !important;
  font-family: var(--text-font-family) !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  transition: all 0.2s ease !important;
  box-sizing: border-box !important;
}

/* Input focus states */
.contact-form .input__field:focus,
.contact-form .select__field:focus,
.contact-form textarea:focus {
  outline: none !important;
  border-color: #47DE47 !important;
  box-shadow: 0 0 0 3px rgba(71, 222, 71, 0.1) !important;
}

/* Input labels */
.contact-form .input__label,
.contact-form .select__label {
  font-family: var(--heading-font-family) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  color: #2A2A2A !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}

/* Placeholder styling */
.contact-form .input__field::placeholder,
.contact-form textarea::placeholder {
  color: #999999 !important;
  font-style: italic !important;
}

/* Textarea specific styling */
.contact-form textarea {
  min-height: 120px !important;
  resize: vertical !important;
  font-family: var(--text-font-family) !important;
}

/* Submit button styling */
.contact-form .button {
  background: #47DE47 !important;
  color: #2A2A2A !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  padding: 0.875rem 2.5rem !important;
  font-family: var(--heading-font-family) !important;
  font-weight: 700 !important;
  font-size: 1rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin-top: 1rem !important;
  text-decoration: none !important;
  display: inline-block !important;
}

.contact-form .button:hover {
  background: #2A2A2A !important;
  color: #47DE47 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(42, 42, 42, 0.2) !important;
}

/* Success and error messages */
.contact-form .banner {
  border-radius: 8px !important;
  padding: 1rem !important;
  margin-bottom: 1.5rem !important;
  font-family: var(--text-font-family) !important;
  font-weight: 600 !important;
}

.contact-form .banner--success {
  background: rgba(71, 222, 71, 0.1) !important;
  border: 2px solid #47DE47 !important;
  color: #2A2A2A !important;
}

.contact-form .banner--error {
  background: rgba(231, 76, 60, 0.1) !important;
  border: 2px solid #E74C3C !important;
  color: #2A2A2A !important;
}

/* Required field indicators */
.contact-form .input__label--required::after,
.contact-form .select__label--required::after {
  content: " *" !important;
  color: #E74C3C !important;
  margin-left: 0.25rem !important;
}

/* Mobile responsive adjustments */
@media screen and (max-width: 749px) {
  .shopify-section--contact {
    padding: 2rem 0 !important;
  }
  
  .shopify-section--contact .section-stack {
    padding: 0 1rem !important;
  }
  
  .contact-form {
    padding: 2rem !important;
    border-radius: 8px !important;
  }
  
  .shopify-section--contact .h2,
  .shopify-section--contact h2 {
    font-size: 2.5rem !important;
    margin-bottom: 1rem !important;
  }
  
  .contact-form .input-row {
    grid-template-columns: 1fr !important;
    gap: 0 !important;
  }
  
  .contact-form .button {
    width: 100% !important;
    padding: 1rem !important;
    font-size: 0.875rem !important;
  }
}

/* Additional form validation styling */
.contact-form .input--error .input__field,
.contact-form .select--error .select__field {
  border-color: #E74C3C !important;
  box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
}

/* Form loading state */
.contact-form .button--loading {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}

/* Select dropdown styling */
.contact-form .select__field {
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%232A2A2A' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E") !important;
  background-repeat: no-repeat !important;
  background-position: right 1rem center !important;
  background-size: 1rem !important;
  padding-right: 3rem !important;
}

/* Input field focus ring */
.contact-form .input__field:focus-visible,
.contact-form .select__field:focus-visible,
.contact-form textarea:focus-visible {
  outline: 2px solid #47DE47 !important;
  outline-offset: 2px !important;
}

/* Form accessibility improvements */
.contact-form .input__field:invalid,
.contact-form .select__field:invalid,
.contact-form textarea:invalid {
  box-shadow: none !important;
}

/* Ensure proper spacing between form elements */
.contact-form > * + * {
  margin-top: 1.5rem !important;
}

/* Additional input styling for better consistency */
.contact-form input[type="text"],
.contact-form input[type="email"],
.contact-form select,
.contact-form textarea {
  width: 100% !important;
  padding: 0.875rem 1rem !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  background: #FFFFFF !important;
  color: #2A2A2A !important;
  font-family: var(--text-font-family) !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  transition: all 0.2s ease !important;
  box-sizing: border-box !important;
}

.contact-form input[type="text"]:focus,
.contact-form input[type="email"]:focus,
.contact-form select:focus,
.contact-form textarea:focus {
  outline: none !important;
  border-color: #47DE47 !important;
  box-shadow: 0 0 0 3px rgba(71, 222, 71, 0.1) !important;
}

/* Form labels - catch all variations */
.contact-form label {
  font-family: var(--heading-font-family) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  color: #2A2A2A !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}

/* Button styling - catch all variations */
.contact-form button,
.contact-form input[type="submit"],
.contact-form [type="submit"] {
  background: #47DE47 !important;
  color: #2A2A2A !important;
  border: 2px solid #2A2A2A !important;
  border-radius: 8px !important;
  padding: 0.875rem 2.5rem !important;
  font-family: var(--heading-font-family) !important;
  font-weight: 700 !important;
  font-size: 1rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  margin-top: 1rem !important;
  text-decoration: none !important;
  display: inline-block !important;
}

.contact-form button:hover,
.contact-form input[type="submit"]:hover,
.contact-form [type="submit"]:hover {
  background: #2A2A2A !important;
  color: #47DE47 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(42, 42, 42, 0.2) !important;
}

/* Section intro text alignment */
.shopify-section--contact .section-stack__intro {
  margin-bottom: 2rem !important;
}

/* Ensure form container has proper styling */
.shopify-section--contact .section-stack__main {
  width: 100% !important;
}

/* Override any default form styling */
.contact-form .form {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure consistent spacing in fieldset */
.contact-form .fieldset > * {
  margin-bottom: 1.5rem !important;
}

.contact-form .fieldset > *:last-child {
  margin-bottom: 0 !important;
}
